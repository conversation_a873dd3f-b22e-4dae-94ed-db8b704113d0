<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\User;
use App\Models\Branch;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Models\AttendanceRequest;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\AttendanceTypeSetting;
use Yajra\DataTables\Facades\DataTables;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class AttendanceRequestController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $branches = Branch::select('id', 'name')->active()->orderBy('name')->get();
        $attendanceTypes = AttendanceTypeSetting::select('id', 'name')->orderBy('name')->get();
        $roles = Role::select('id', 'name')->orderBy('name')->get();
        
        return view('backoffice.attendance-request.index', compact('branches', 'attendanceTypes', 'roles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('backoffice.attendance-request.create-update');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'branch_id' => 'required|exists:branches,id',
            'attendance_date' => 'required|date|after_or_equal:today',
            'attendance_type_id' => 'required|exists:attendance_type_settings,id',
            'reason' => 'required|string|max:1000',
            'attachment' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx|max:2048'
        ]);

        // Check if request already exists for this employee on this date
        $existingRequest = AttendanceRequest::where('employee_id', $request->employee_id)
            ->where('attendance_date', $request->attendance_date)
            ->first();

        if ($existingRequest) {
            return back()->withErrors(['attendance_date' => 'Pengajuan absen untuk karyawan ini pada tanggal tersebut sudah ada.'])->withInput();
        }

        DB::transaction(function () use ($request) {
            $data = [
                'user_id' => Auth::id(),
                'employee_id' => $request->employee_id,
                'branch_id' => $request->branch_id,
                'attendance_date' => $request->attendance_date,
                'attendance_type_id' => $request->attendance_type_id,
                'reason' => $request->reason,
                'status' => 'pending',
            ];

            // Handle file upload
            if ($request->hasFile('attachment')) {
                $file = $request->file('attachment');
                $data['attachment'] = upload_file($file, 'attendance-requests', 'attachment');
            }

            AttendanceRequest::create($data);
        });

        return redirect()->route('attendance-request.index')->with('success', 'Pengajuan absen berhasil dibuat');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $attendanceRequest = AttendanceRequest::with(['user', 'employee', 'branch', 'attendanceType', 'approvedBy'])
            ->findOrFail($id);
            
        return view('backoffice.attendance-request.show', compact('attendanceRequest'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = AttendanceRequest::findOrFail($id);

        // Check if can be edited
        if (!$data->canBeEdited()) {
            return redirect()->route('attendance-request.index')->with('error', 'Pengajuan yang sudah diproses tidak dapat diedit');
        }

        return view('backoffice.attendance-request.create-update', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $attendanceRequest = AttendanceRequest::findOrFail($id);
        
        // Check if can be edited
        if (!$attendanceRequest->canBeEdited()) {
            return redirect()->route('attendance-request.index')->with('error', 'Pengajuan yang sudah diproses tidak dapat diedit');
        }

        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'branch_id' => 'required|exists:branches,id',
            'attendance_date' => [
                'required',
                'date',
                'after_or_equal:today',
                Rule::unique('attendance_requests', 'attendance_date')
                    ->where('employee_id', $request->employee_id)
                    ->ignore($id)
            ],
            'attendance_type_id' => 'required|exists:attendance_type_settings,id',
            'reason' => 'required|string|max:1000',
            'attachment' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx|max:2048'
        ]);

        DB::transaction(function () use ($request, $attendanceRequest) {
            $data = [
                'employee_id' => $request->employee_id,
                'branch_id' => $request->branch_id,
                'attendance_date' => $request->attendance_date,
                'attendance_type_id' => $request->attendance_type_id,
                'reason' => $request->reason,
            ];

            // Handle file upload
            if ($request->hasFile('attachment')) {
                // Delete old file if exists
                if ($attendanceRequest->attachment && file_exists(public_path($attendanceRequest->attachment))) {
                    unlink(public_path($attendanceRequest->attachment));
                }
                
                $file = $request->file('attachment');
                $data['attachment'] = upload_file($file, 'attendance-requests', 'attachment');
            }

            $attendanceRequest->update($data);
        });

        return redirect()->route('attendance-request.index')->with('success', 'Pengajuan absen berhasil diperbarui');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $attendanceRequest = AttendanceRequest::findOrFail($id);
            
            // Check if can be deleted
            if (!$attendanceRequest->canBeEdited()) {
                return response()->json(['status' => false, 'message' => 'Pengajuan yang sudah diproses tidak dapat dihapus'], 400);
            }

            DB::transaction(function () use ($attendanceRequest) {
                // Delete attachment file if exists
                if ($attendanceRequest->attachment && file_exists(public_path($attendanceRequest->attachment))) {
                    unlink(public_path($attendanceRequest->attachment));
                }
                
                $attendanceRequest->delete();
            });

            return response()->json(['status' => true, 'message' => 'Pengajuan absen berhasil dihapus'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * DataTable for attendance requests
     */
    public function dataTable(Request $request)
    {
        $data = AttendanceRequest::with(['user', 'employee', 'branch', 'attendanceType', 'approvedBy'])
            ->select(
                'id',
                'user_id',
                'employee_id',
                'branch_id',
                'attendance_date',
                'attendance_type_id',
                'reason',
                'status',
                'approved_by',
                'approved_at',
                'created_at'
            )
            ->accessibleByUser(Auth::id()) // Only show requests for accessible branches
            ->filter($request)
            ->latest();

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('checkbox', function ($data) {
                // Disable checkbox for approved/rejected requests
                $disabled = ($data->status !== 'pending') ? 'disabled' : '';
                $class = ($data->status !== 'pending') ? 'form-check-input select-item disabled' : 'form-check-input select-item';
                return '<input type="checkbox" class="' . $class . '" value="' . $data->id . '" ' . $disabled . '>';
            })
            ->addColumn('employee_name', function ($data) {
                return ($data->employee->nama ?? '-') . 
                       ($data->employee->kode ? ' (' . $data->employee->kode . ')' : '');
            })
            ->addColumn('branch_name', function ($data) {
                return $data->branch->name ?? '-';
            })
            ->addColumn('attendance_type_name', function ($data) {
                return $data->attendanceType->name ?? '-';
            })
            ->addColumn('created_by', function ($data) {
                return $data->user->nama ?? '-';
            })
            ->editColumn('created_at', function ($data) {
                return $data->created_at_formatted;
            })
            ->addColumn('approved_by_name', function ($data) {
                return $data->approvedBy->nama ?? '-';
            })
            ->addColumn('status_badge', function ($data) {
                return $data->status_badge;
            })
            ->addColumn('action', function ($data) {
                $action_button = '';

                // View button
                $action_button .= "<li>
                                    <a class='dropdown-item' href='" . route('attendance-request.show', $data->id) . "'>
                                        <i class='feather feather-eye me-3'></i>
                                        <span>Lihat</span>
                                    </a>
                                </li>";

                // Edit button (only for pending requests)
                if ($data->canBeEdited() && canPermission('Pengajuan Absen.Update')) {
                    $action_button .= "<li>
                                        <a class='dropdown-item' href='" . route('attendance-request.edit', $data->id) . "'>
                                            <i class='feather feather-edit me-3'></i>
                                            <span>Edit</span>
                                        </a>
                                    </li>";
                }

                // Approval buttons (only for pending requests)
                if ($data->canBeProcessed() && canPermission('Pengajuan Absen.Approve')) {
                    $action_button .= "<li>
                                        <a class='dropdown-item approveData' href='javascript:void(0)' data-id='$data->id'>
                                            <i class='feather feather-check me-3'></i>
                                            <span>Setujui</span>
                                        </a>
                                    </li>";
                    $action_button .= "<li>
                                        <a class='dropdown-item rejectData' href='javascript:void(0)' data-id='$data->id'>
                                            <i class='feather feather-x me-3'></i>
                                            <span>Tolak</span>
                                        </a>
                                    </li>";
                }

                // Delete button (only for pending requests)
                if ($data->canBeEdited() && canPermission('Pengajuan Absen.Delete')) {
                    $action_button .= "<li>
                                        <a class='dropdown-item deleteData' href='javascript:void(0)' data-id='$data->id'>
                                            <i class='feather feather-trash-2 me-3'></i>
                                            <span>Hapus</span>
                                        </a>
                                    </li>";
                }

                if ($action_button) {
                    $action = "<div class='hstack gap-2'>
                                <div class='dropdown dropdown-overflow'>
                                    <a href='javascript:void(0)'
                                        class='avatar-text avatar-md btn-dropdown'
                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                        <i class='feather feather-more-horizontal'></i>
                                    </a>
                                    <ul class='dropdown-menu'>
                                        $action_button
                                    </ul>
                                </div>
                            </div>";
                    return $action;
                }

                return '-';
            })
            ->rawColumns(['checkbox', 'status_badge', 'action'])
            ->make(true);
    }

    /**
     * Approve attendance request
     */
    public function approve(string $id)
    {
        try {
            $attendanceRequest = AttendanceRequest::findOrFail($id);

            if (!$attendanceRequest->canBeProcessed()) {
                return response()->json(['status' => false, 'message' => 'Pengajuan ini tidak dapat diproses'], 400);
            }

            DB::transaction(function () use ($attendanceRequest) {
                $attendanceRequest->approve(Auth::id());
            });

            return response()->json(['status' => true, 'message' => 'Pengajuan absen berhasil disetujui'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Reject attendance request
     */
    public function reject(Request $request, string $id)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500'
        ]);

        try {
            $attendanceRequest = AttendanceRequest::findOrFail($id);

            if (!$attendanceRequest->canBeProcessed()) {
                return response()->json(['status' => false, 'message' => 'Pengajuan ini tidak dapat diproses'], 400);
            }

            DB::transaction(function () use ($attendanceRequest, $request) {
                $attendanceRequest->reject(Auth::id(), $request->rejection_reason);
            });

            return response()->json(['status' => true, 'message' => 'Pengajuan absen berhasil ditolak'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Get employees by branch (for AJAX)
     */
    public function getEmployeesByBranch(Request $request)
    {
        $branchId = $request->branch_id;
        $userId = Auth::id();

        // Check if user has access to this branch
        $hasAccess = Branch::whereHas('userAccessBranches', function($q) use ($userId) {
            $q->where('user_id', $userId);
        })->where('id', $branchId)->exists();

        if (!$hasAccess) {
            return response()->json(['error' => 'Anda tidak memiliki akses ke cabang ini'], 403);
        }

        $employees = Employee::select('id', 'nama', 'kode')
            ->where('branch_id', $branchId)
            ->where('status', 'active')
            ->orderBy('nama')
            ->get()
            ->map(function ($employee) {
                return [
                    'id' => $employee->id,
                    'nama' => $employee->nama . ($employee->kode ? ' (' . $employee->kode . ')' : ''),
                    'kode' => $employee->kode
                ];
            });

        return response()->json($employees);
    }

    /**
     * Export to Excel
     */
    public function exportExcel(Request $request)
    {
        $data = AttendanceRequest::with(['user', 'employee', 'branch', 'attendanceType', 'approvedBy'])
            ->accessibleByUser(Auth::id())
            ->filter($request)
            ->orderBy('attendance_date', 'desc')
            ->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headers
        $sheet->setCellValue('A1', 'No');
        $sheet->setCellValue('B1', 'Tanggal Pengajuan');
        $sheet->setCellValue('C1', 'Data Karyawan');
        $sheet->setCellValue('D1', 'Cabang');
        $sheet->setCellValue('E1', 'Tanggal Absen');
        $sheet->setCellValue('F1', 'Jenis Absen');
        $sheet->setCellValue('G1', 'Alasan Pengajuan');
        $sheet->setCellValue('H1', 'Status');
        $sheet->setCellValue('I1', 'Diajukan Oleh');
        $sheet->setCellValue('J1', 'Disetujui/Ditolak Oleh');
        $sheet->setCellValue('K1', 'Tanggal Persetujuan');
        $sheet->setCellValue('L1', 'Alasan Penolakan');

        // Style headers
        $headerStyle = [
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'E2E8F0']
            ]
        ];
        $sheet->getStyle('A1:L1')->applyFromArray($headerStyle);

        // Fill data
        $row = 2;
        foreach ($data as $index => $item) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $item->created_at_formatted);
            $sheet->setCellValue('C' . $row, $item->employee->nama ?? '-');
            $sheet->setCellValue('D' . $row, $item->branch->name ?? '-');
            $sheet->setCellValue('E' . $row, $item->attendance_date_formatted);
            $sheet->setCellValue('F' . $row, $item->attendanceType->name ?? '-');
            $sheet->setCellValue('G' . $row, $item->reason);
            $sheet->setCellValue('H' . $row, ucfirst($item->status));
            $sheet->setCellValue('I' . $row, $item->user->nama ?? '-');
            $sheet->setCellValue('J' . $row, $item->approvedBy->nama ?? '-');
            $sheet->setCellValue('K' . $row, $item->approved_at_formatted);
            $sheet->setCellValue('L' . $row, $item->rejection_reason ?? '-');
            $row++;
        }

        // Auto size columns
        foreach (range('A', 'L') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'pengajuan_absen_' . date('Y-m-d_H-i-s') . '.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    /**
     * Bulk approve attendance requests
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:attendance_requests,id'
        ]);

        try {
            $approved = 0;
            $failed = 0;

            DB::transaction(function () use ($request, &$approved, &$failed) {
                foreach ($request->ids as $id) {
                    $attendanceRequest = AttendanceRequest::find($id);
                    if ($attendanceRequest && $attendanceRequest->canBeProcessed()) {
                        $attendanceRequest->approve(Auth::id());
                        $approved++;
                    } else {
                        $failed++;
                    }
                }
            });

            $message = "Berhasil menyetujui {$approved} pengajuan";
            if ($failed > 0) {
                $message .= ", {$failed} pengajuan tidak dapat diproses (sudah diproses sebelumnya)";
            }

            return response()->json(['status' => true, 'message' => $message], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Bulk reject attendance requests
     */
    public function bulkReject(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:attendance_requests,id',
            'rejection_reason' => 'required|string|max:500'
        ]);

        try {
            $rejected = 0;
            $failed = 0;

            DB::transaction(function () use ($request, &$rejected, &$failed) {
                foreach ($request->ids as $id) {
                    $attendanceRequest = AttendanceRequest::find($id);
                    if ($attendanceRequest && $attendanceRequest->canBeProcessed()) {
                        $attendanceRequest->reject(Auth::id(), $request->rejection_reason);
                        $rejected++;
                    } else {
                        $failed++;
                    }
                }
            });

            $message = "Berhasil menolak {$rejected} pengajuan";
            if ($failed > 0) {
                $message .= ", {$failed} pengajuan tidak dapat diproses (sudah diproses sebelumnya)";
            }

            return response()->json(['status' => true, 'message' => $message], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    /**
     * Bulk delete attendance requests
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:attendance_requests,id'
        ]);

        try {
            $deleted = 0;
            $failed = 0;

            DB::transaction(function () use ($request, &$deleted, &$failed) {
                foreach ($request->ids as $id) {
                    $attendanceRequest = AttendanceRequest::find($id);
                    if ($attendanceRequest && $attendanceRequest->canBeEdited()) {
                        // Delete attachment file if exists
                        if ($attendanceRequest->attachment && file_exists(public_path($attendanceRequest->attachment))) {
                            unlink(public_path($attendanceRequest->attachment));
                        }
                        $attendanceRequest->delete();
                        $deleted++;
                    } else {
                        $failed++;
                    }
                }
            });

            $message = "Berhasil menghapus {$deleted} pengajuan";
            if ($failed > 0) {
                $message .= ", {$failed} pengajuan tidak dapat dihapus (sudah diproses)";
            }

            return response()->json(['status' => true, 'message' => $message], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }
}
