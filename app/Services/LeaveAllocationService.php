<?php

namespace App\Services;

use App\Models\Employee;
use App\Models\LeaveOffSetting;
use App\Models\EmployeeLeaveEntitlement;
use Carbon\Carbon;

class LeaveAllocationService
{
    /**
     * Calculate and allocate leave entitlements for an employee
     */
    public function allocateLeaveForEmployee(Employee $employee, int $year = null): array
    {
        $year = $year ?? now()->year;
        $allocations = [];
        
        // Get all leave settings
        $leaveSettings = LeaveOffSetting::all();
        
        foreach ($leaveSettings as $leaveSetting) {
            $allocation = $this->calculateLeaveAllocation($employee, $leaveSetting, $year);
            
            if ($allocation['allocated_days'] > 0) {
                $entitlement = $this->createOrUpdateEntitlement($employee, $leaveSetting, $year, $allocation);
                $allocations[] = $entitlement;
            }
        }
        
        return $allocations;
    }

    /**
     * Calculate leave allocation based on employee service length and leave settings
     */
    private function calculateLeaveAllocation(Employee $employee, LeaveOffSetting $leaveSetting, int $year): array
    {
        $serviceMonths = $this->calculateServiceMonths($employee, $year);
        $allocatedDays = 0;
        $validFrom = null;
        $validUntil = null;

        // Parse min_service_length to determine eligibility
        if ($leaveSetting->min_service_length) {
            $minServiceMonths = $this->parseServiceLength($leaveSetting->min_service_length);
            
            if ($serviceMonths >= $minServiceMonths) {
                $allocatedDays = $leaveSetting->max_leave_days;
                
                // Set validity period
                $validFrom = Carbon::create($year, 1, 1);
                $validUntil = Carbon::create($year, 12, 31);
                
                // If employee joined during the year, adjust the allocation
                if ($employee->join_date && $employee->join_date->year == $year) {
                    $monthsInYear = 12 - $employee->join_date->month + 1;
                    $allocatedDays = round(($allocatedDays * $monthsInYear) / 12);
                    $validFrom = $employee->join_date;
                }
            }
        } else {
            // No minimum service length requirement
            $allocatedDays = $leaveSetting->max_leave_days;
            $validFrom = Carbon::create($year, 1, 1);
            $validUntil = Carbon::create($year, 12, 31);
            
            // If employee joined during the year, adjust the allocation
            if ($employee->join_date && $employee->join_date->year == $year) {
                $monthsInYear = 12 - $employee->join_date->month + 1;
                $allocatedDays = round(($allocatedDays * $monthsInYear) / 12);
                $validFrom = $employee->join_date;
            }
        }

        return [
            'allocated_days' => $allocatedDays,
            'valid_from' => $validFrom,
            'valid_until' => $validUntil,
            'service_months' => $serviceMonths,
        ];
    }

    /**
     * Calculate service months for an employee up to a specific year
     */
    private function calculateServiceMonths(Employee $employee, int $year): int
    {
        if (!$employee->join_date) {
            return 0;
        }

        $joinDate = $employee->join_date;
        $endDate = Carbon::create($year, 12, 31);
        
        // If employee joined after the year, no service months
        if ($joinDate->year > $year) {
            return 0;
        }
        
        // If employee resigned before the year, calculate up to resignation date
        if ($employee->resign_date && $employee->resign_date->year < $year) {
            $endDate = $employee->resign_date;
        }
        
        return $joinDate->diffInMonths($endDate);
    }

    /**
     * Parse service length string to months
     * Examples: "6 months", "1 year", "2 years", "18 months"
     */
    private function parseServiceLength(string $serviceLength): int
    {
        $serviceLength = strtolower(trim($serviceLength));
        
        // Extract number and unit
        if (preg_match('/(\d+)\s*(month|months|year|years)/', $serviceLength, $matches)) {
            $number = (int) $matches[1];
            $unit = $matches[2];
            
            if (in_array($unit, ['year', 'years'])) {
                return $number * 12; // Convert years to months
            } else {
                return $number; // Already in months
            }
        }
        
        return 0; // Default if parsing fails
    }

    /**
     * Create or update leave entitlement record
     */
    private function createOrUpdateEntitlement(Employee $employee, LeaveOffSetting $leaveSetting, int $year, array $allocation): EmployeeLeaveEntitlement
    {
        $entitlement = EmployeeLeaveEntitlement::updateOrCreate(
            [
                'employee_id' => $employee->id,
                'leave_off_setting_id' => $leaveSetting->id,
                'year' => $year,
            ],
            [
                'allocated_days' => $allocation['allocated_days'],
                'valid_from' => $allocation['valid_from'],
                'valid_until' => $allocation['valid_until'],
                'remaining_days' => $allocation['allocated_days'], // Reset remaining days
                'notes' => "Auto-allocated based on {$allocation['service_months']} months of service",
            ]
        );

        return $entitlement;
    }

    /**
     * Allocate leave for all active employees for a specific year
     */
    public function allocateLeaveForAllEmployees(int $year = null): array
    {
        $year = $year ?? now()->year;
        $results = [];
        
        $employees = Employee::active()->get();
        
        foreach ($employees as $employee) {
            $allocations = $this->allocateLeaveForEmployee($employee, $year);
            $results[$employee->id] = $allocations;
        }
        
        return $results;
    }

    /**
     * Get leave summary for an employee
     */
    public function getEmployeeLeaveSummary(Employee $employee, int $year = null): array
    {
        $year = $year ?? now()->year;
        
        $entitlements = $employee->leaveEntitlements()
            ->with('leaveOffSetting')
            ->forYear($year)
            ->get();
        
        $summary = [];
        
        foreach ($entitlements as $entitlement) {
            $summary[] = [
                'leave_type' => $entitlement->leaveOffSetting->name,
                'leave_code' => $entitlement->leaveOffSetting->code,
                'allocated_days' => $entitlement->allocated_days,
                'used_days' => $entitlement->used_days,
                'remaining_days' => $entitlement->remaining_days,
                'valid_from' => $entitlement->valid_from,
                'valid_until' => $entitlement->valid_until,
            ];
        }
        
        return $summary;
    }

    /**
     * Refresh leave allocations for a specific year
     * This will recalculate and update all allocations
     */
    public function refreshLeaveAllocations(int $year = null): array
    {
        $year = $year ?? now()->year;
        
        // Delete existing allocations for the year
        EmployeeLeaveEntitlement::forYear($year)->delete();
        
        // Reallocate for all employees
        return $this->allocateLeaveForAllEmployees($year);
    }
}
