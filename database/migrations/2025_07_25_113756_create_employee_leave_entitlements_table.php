<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employee_leave_entitlements', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('employee_id')->index();
            $table->uuid('leave_off_setting_id')->index();
            $table->integer('year');
            $table->integer('allocated_days')->default(0);
            $table->integer('used_days')->default(0);
            $table->integer('remaining_days')->default(0);
            $table->date('valid_from')->nullable();
            $table->date('valid_until')->nullable();
            $table->text('notes')->nullable();
            $table->softDeletes();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('employee_id')->references('id')->on('employees')->onDelete('cascade');
            $table->foreign('leave_off_setting_id')->references('id')->on('leave_off_settings')->onDelete('cascade');

            // Unique constraint to prevent duplicate entitlements for same employee, leave type, and year
            $table->unique(['employee_id', 'leave_off_setting_id', 'year'], 'unique_employee_leave_year');

            // Indexes for better performance
            $table->index(['employee_id', 'year']);
            $table->index(['year', 'leave_off_setting_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_leave_entitlements');
    }
};
