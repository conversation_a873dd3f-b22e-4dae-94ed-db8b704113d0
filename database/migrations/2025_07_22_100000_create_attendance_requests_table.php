<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendance_requests', function (Blueprint $table) {
            $table->uuid('id')->primary();
            // $table->uuid('user_id')->index(); // User yang mengajukan
            $table->uuid('employee_id')->index(); // Karyawan yang diajukan absennya
            $table->uuid('branch_id')->index(); // Cabang
            $table->date('attendance_date')->index(); // Tanggal absen
            $table->uuid('attendance_type_id')->index(); // Jenis absen (referensi ke attendance_type_settings)
            $table->text('reason'); // Keterangan/alasan
            $table->string('attachment')->nullable(); // Path file lampiran (optional)
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending')->index(); // Status approval
            $table->uuid('approved_by')->nullable()->index(); // User yang approve/reject
            $table->timestamp('approved_at')->nullable(); // Waktu approve/reject
            $table->text('rejection_reason')->nullable(); // Alasan reject (jika ditolak)
            $table->timestamps();

            // Unique constraint to prevent duplicate requests for same employee on same date
            $table->unique(['employee_user_id', 'attendance_date'], 'unique_employee_attendance_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_requests');
    }
};
