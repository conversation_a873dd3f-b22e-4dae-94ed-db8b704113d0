<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('attendance_requests', function (Blueprint $table) {
            // Add new employee_id column
            $table->uuid('employee_id')->nullable()->after('user_id');
            
            // Add foreign key constraint
            $table->foreign('employee_id')->references('id')->on('employees')->onDelete('cascade');
            
            // Add index for better performance
            $table->index('employee_id');
        });
        
        // Copy data from employee_user_id to employee_id if needed
        // This would require custom logic based on your data mapping
        
        Schema::table('attendance_requests', function (Blueprint $table) {
            // Drop the old column after data migration
            $table->dropColumn('employee_user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('attendance_requests', function (Blueprint $table) {
            // Add back the old column
            $table->uuid('employee_user_id')->nullable()->after('user_id');
            
            // Drop the new column and its constraints
            $table->dropForeign(['employee_id']);
            $table->dropIndex(['employee_id']);
            $table->dropColumn('employee_id');
        });
    }
};
